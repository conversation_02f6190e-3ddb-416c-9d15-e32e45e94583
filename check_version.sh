#aws sso login --profile TymeX-AWS-Engineer-Wks

version=$(aws codeartifact list-package-versions \
        --domain tymecentral \
        --repository libs-release \
        --format maven \
        --namespace "com.tymex.core" \
        --package "core-remote-config" \
        --profile TymeX-AWS-Engineer-Wks\
        --query "versions[].version" \
        --output text)
        #--output text 2>/dev/null | tr '\t' '\n' | sort -V | tail -n 1 || echo "")

echo "version = $version"