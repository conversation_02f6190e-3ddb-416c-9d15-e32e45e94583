#!/bin/bash

# ======== Configuration ========
AWS_PROFILE="tb-pipeline"
#SSM_KEY="/tp/common/tymex_mobile_keys_gosa_uat"
SSM_KEY="/tp/common/tymex_mobile_keys_gosa_prod"


# ======== Execution ========

# Login to AWS first
#aws sso login --profile $AWS_PROFILE
#aws-azure-login --profile $AWS_PROFILE --mode gui

TYMEX_MOBILE_KEYS=$(aws ssm get-parameter --name "$SSM_KEY" --profile $AWS_PROFILE --with-decryption | jq -r '.Parameter.Value')

if [ -z "$TYMEX_MOBILE_KEYS" ];
then
  echo "TYMEX_MOBILE_KEYS is not set"
  exit 1
fi
