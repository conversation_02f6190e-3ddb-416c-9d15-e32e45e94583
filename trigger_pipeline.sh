#!/bin/bash

# ======== Configuration ========
AWS_REGION="ap-southeast-1"
AWS_PROFILE="TymeX-AWS-Engineer-Wks"

# List of pipeline names
PIPELINES=(
  "mx-mobile-core-rgp"
  "mx-channel-design-system-rgp"
  "mx-growth-artifact-rgp"
  "mx-onc-mobile-rgp"
  "mx-origination-mobile-rgp"
  "mx-customers-mobile-rgp"
  "mx-experiences-mobile-rgp"
  "mx-mobile-transfer-rgp"
  "mx-mobile-fund-in-rgp"
  "mx-mobile-transactional-account-service-rgp"
  "mx-mobile-card-rgp"
  "mx-sim-mobile-rgp"
  "mx-mobile-savings-rgp"
  "mx-lend-mobile-rgp"
  "mx-mobile-third-party-artifact-rgp"
)

# ======== Execution ========

# Login to AWS first
# aws sso login --profile TymeX-AWS-Engineer-Wks

echo "Starting pipeline triggers..."

for PIPELINE_NAME in "${PIPELINES[@]}"; do
  echo "Triggering pipeline: $PIPELINE_NAME"

  # Trigger the pipeline execution
  if [[ -n "$AWS_PROFILE" ]]; then
    RESPONSE=$(aws codepipeline start-pipeline-execution \
      --name "$PIPELINE_NAME" \
      --region "$AWS_REGION" \
      --profile "$AWS_PROFILE" 2>&1)
  else
    RESPONSE=$(aws codepipeline start-pipeline-execution \
      --name "$PIPELINE_NAME" \
      --region "$AWS_REGION" 2>&1)
  fi

  # Check for success or failure
  if [[ $? -eq 0 ]]; then
    echo "✅ Successfully triggered $PIPELINE_NAME"
    echo "$RESPONSE"
  else
    echo "❌ Failed to trigger $PIPELINE_NAME"
    echo "$RESPONSE"
  fi

  echo "---------------------------"
done

echo "All pipelines attempted."