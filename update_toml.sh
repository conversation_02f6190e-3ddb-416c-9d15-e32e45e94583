#!/bin/bash

TOML_FILE="./gradle/libsCustom.versions.toml"

AWS_PROFILE="TymeX-AWS-Engineer-Wks"
AWS_DOMAIN="tymecentral"
AWS_DOMAIN_OWNER="672490406318"
AWS_REPOSITORY="libs-release"
FORMAT="maven"

echo "📦 Updating versions in $TOML_FILE..."

# Extract module definitions
MODULE_LINES=$(awk '/\[libraries\]/ {in_libraries=1; next} /^\[.*\]/ {in_libraries=0} in_libraries && /=/ { print }' "$TOML_FILE")

while read -r LINE; do
  KEY=$(echo "$LINE" | cut -d= -f1 | xargs)
  MODULE_STR=$(echo "$LINE" | grep -o 'module *= *"[^"]\+"' | sed 's/module *= *"\(.*\)"/\1/')

  GROUP_ID=$(echo "$MODULE_STR" | cut -d: -f1)
  ARTIFACT_ID=$(echo "$MODULE_STR" | cut -d: -f2)

  if [[ -z "$GROUP_ID" || -z "$ARTIFACT_ID" ]]; then
    echo "⚠️  Skipping $KEY — could not parse module"
    continue
  fi

  echo "🔍 Fetching latest version for $ARTIFACT_ID in $GROUP_ID..."

  # Query AWS CodeArtifact
  LATEST_VERSION=$(aws codeartifact list-package-versions \
    --domain "$AWS_DOMAIN" \
    --domain-owner "$AWS_DOMAIN_OWNER" \
    --repository "$AWS_REPOSITORY" \
    --namespace "$GROUP_ID" \
    --package "$ARTIFACT_ID" \
    --format "$FORMAT" \
    --profile "$AWS_PROFILE" \
    --query 'versions[?status==`Published`].version' \
    --output text | tr '\t' '\n' | sort -V | tail -n 1)

  if [[ -z "$LATEST_VERSION" || "$LATEST_VERSION" == "None" ]]; then
    echo "❌ Package not found: $GROUP_ID:$ARTIFACT_ID — skipping"
    continue
  fi

  echo "✅ $KEY → $LATEST_VERSION"

  # Update version in [versions] section
  sed -i '' -E "s/^($KEY *= *)\"[^\"]+\"/\1\"$LATEST_VERSION\"/" "$TOML_FILE"
done <<< "$MODULE_LINES"

echo "🎉 Done. All available versions updated in $TOML_FILE."
