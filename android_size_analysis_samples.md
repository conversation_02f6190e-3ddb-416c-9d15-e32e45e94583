# Android APK/AAB Size Analysis - Sample Outputs

This document shows sample outputs from Android's built-in size analysis tools.

## 1. APK Analysis with `apkanalyzer`

### Basic APK Information

```bash
# Get APK summary
$ apkanalyzer apk summary MyApp-release.apk
com.mycompany.myapp 42 2.1.0

# Get total file size
$ apkanalyzer apk file-size MyApp-release.apk
45678912

# Get human-readable file size
$ apkanalyzer -h apk file-size MyApp-release.apk
43.5 MB

# Get estimated download size
$ apkanalyzer -h apk download-size MyApp-release.apk
32.1 MB
```

### Detailed APK Breakdown

```bash
# List all files in APK
$ apkanalyzer files list MyApp-release.apk
/
/classes.dex
/classes2.dex
/classes3.dex
/lib/
/lib/arm64-v8a/
/lib/arm64-v8a/libnative.so
/lib/arm64-v8a/libflutter.so
/lib/armeabi-v7a/
/lib/armeabi-v7a/libnative.so
/lib/armeabi-v7a/libflutter.so
/assets/
/assets/flutter_assets/
/assets/flutter_assets/fonts/
/assets/flutter_assets/packages/
/res/
/res/drawable-hdpi/
/res/drawable-xhdpi/
/res/drawable-xxhdpi/
/res/drawable-xxxhdpi/
/res/layout/
/res/values/
/AndroidManifest.xml
/resources.arsc
/META-INF/
/META-INF/CERT.SF
/META-INF/CERT.RSA
/META-INF/MANIFEST.MF
```

### DEX File Analysis

```bash
# List DEX files
$ apkanalyzer dex list MyApp-release.apk
classes.dex
classes2.dex
classes3.dex

# Get method count per DEX
$ apkanalyzer dex references MyApp-release.apk
classes.dex 65432
classes2.dex 23456
classes3.dex 12345
Total: 101233

# Get package breakdown with sizes
$ apkanalyzer dex packages --defined-only MyApp-release.apk
P d 245 1234 1245678 com
P d 245 1234 1245678 com.mycompany
P d 245 1234 1245678 com.mycompany.myapp
C d 12 45 23456 com.mycompany.myapp.MainActivity
M d 1 2 1234 com.mycompany.myapp.MainActivity void onCreate(Bundle)
M d 1 3 2345 com.mycompany.myapp.MainActivity void onResume()
P d 89 ********** com.mycompany.myapp.network
C d 5 12 12345 com.mycompany.myapp.network.ApiClient
P d 34 ********** com.mycompany.myapp.ui
C d 8 23 34567 com.mycompany.myapp.ui.HomeFragment
```

### Resource Analysis

```bash
# Get resource packages
$ apkanalyzer resources packages MyApp-release.apk
com.mycompany.myapp
android

# Get string resources
$ apkanalyzer resources names --type string MyApp-release.apk
app_name
welcome_message
error_network
button_submit
dialog_title

# Get drawable resources
$ apkanalyzer resources names --type drawable MyApp-release.apk
ic_launcher
ic_home
ic_settings
background_splash
button_background
```

### APK Comparison

```bash
# Compare two APK versions
$ apkanalyzer -h apk compare MyApp-v1.apk MyApp-v2.apk
45678912 48234567 2555655 /
12345678 13456789 1111111 /classes.dex
8765432 9876543 1111111 /classes2.dex
3456789 3456789 0 /classes3.dex
15678901 16789012 1110111 /lib/
7890123 8901234 1011111 /lib/arm64-v8a/libnative.so
7788901 7788901 0 /lib/armeabi-v7a/libnative.so
2345678 3456789 1111111 /assets/
1234567 1234567 0 /res/
567890 678901 111011 /AndroidManifest.xml
890123 901234 11111 /resources.arsc

# Compare with differences only
$ apkanalyzer -h apk compare --different-only MyApp-v1.apk MyApp-v2.apk
45678912 48234567 2555655 /
12345678 13456789 1111111 /classes.dex
8765432 9876543 1111111 /classes2.dex
15678901 16789012 1110111 /lib/
7890123 8901234 1011111 /lib/arm64-v8a/libnative.so
2345678 3456789 1111111 /assets/
567890 678901 111011 /AndroidManifest.xml
890123 901234 11111 /resources.arsc
```

## 2. AAB Analysis with `bundletool`

### Basic AAB Size Analysis

```bash
# Get total size estimate for AAB
$ bundletool get-size total --aab=MyApp-release.aab
MIN,MAX
23456789,45678912

# Get size with human-readable format
$ bundletool get-size total --aab=MyApp-release.aab --human-readable-sizes
MIN,MAX
22.4 MB,43.5 MB

# Get size for specific device configuration
$ bundletool get-size total --aab=MyApp-release.aab --device-spec=pixel6.json
MIN,MAX
28901234,35678901

# Get size breakdown by dimensions
$ bundletool get-size total --aab=MyApp-release.aab --dimensions=SDK,ABI,SCREEN_DENSITY
DIMENSION,MIN,MAX
SDK,23456789,45678912
ABI,25678901,43456789
SCREEN_DENSITY,23456789,44567890
```

### Detailed AAB Breakdown

```bash
# Generate APKs from AAB for analysis
$ bundletool build-apks --bundle=MyApp-release.aab --output=MyApp.apks

# Extract APKs for specific device
$ bundletool extract-apks --apks=MyApp.apks --output-dir=extracted_apks/ --device-spec=pixel6.json

# Get size of extracted APKs
$ bundletool get-size total --apks=MyApp.apks
MIN,MAX
28901234,35678901

# Get size with module breakdown
$ bundletool get-size total --apks=MyApp.apks --modules=base,feature_camera,feature_chat
MODULE,MIN,MAX
base,18901234,25678901
feature_camera,5678901,6789012
feature_chat,4321098,3210987
```

## 3. Combined Analysis Script Output

Here's what a comprehensive analysis script might output:

```bash
#!/bin/bash
# Android Size Analysis Report
# Generated: 2024-01-15 14:30:22

=== APK ANALYSIS REPORT ===
App: MyApp v2.1.0 (Build 42)
Package: com.mycompany.myapp

OVERALL SIZE:
- APK File Size: 43.5 MB (45,678,912 bytes)
- Estimated Download: 32.1 MB
- Uncompressed Size: 67.8 MB

DEX FILES:
- classes.dex: 12.5 MB (65,432 methods)
- classes2.dex: 8.9 MB (23,456 methods)  
- classes3.dex: 4.2 MB (12,345 methods)
- Total Methods: 101,233

NATIVE LIBRARIES:
- arm64-v8a: 15.2 MB
  - libnative.so: 8.5 MB
  - libflutter.so: 6.7 MB
- armeabi-v7a: 14.8 MB
  - libnative.so: 8.1 MB
  - libflutter.so: 6.7 MB

RESOURCES:
- resources.arsc: 890 KB
- res/ folder: 2.3 MB
- assets/ folder: 3.4 MB

TOP PACKAGES BY SIZE:
1. com.mycompany.myapp: 1.2 MB (245 classes)
2. com.google.android.gms: 890 KB (123 classes)
3. androidx.core: 567 KB (89 classes)
4. com.squareup.retrofit2: 234 KB (45 classes)
5. io.reactivex: 123 KB (34 classes)

=== AAB ANALYSIS REPORT ===
Bundle: MyApp-release.aab

SIZE ESTIMATES:
- Minimum Install: 22.4 MB
- Maximum Install: 43.5 MB
- Average Install: 28.7 MB

BY DEVICE TYPE:
- Phone (hdpi): 24.5 MB
- Phone (xhdpi): 26.8 MB  
- Phone (xxhdpi): 28.9 MB
- Tablet (xhdpi): 31.2 MB

BY ARCHITECTURE:
- arm64-v8a only: 28.1 MB
- armeabi-v7a only: 27.8 MB
- Universal: 43.5 MB

MODULE BREAKDOWN:
- base: 24.5 MB (always installed)
- feature_camera: 6.8 MB (on-demand)
- feature_chat: 4.1 MB (on-demand)
- feature_premium: 2.3 MB (conditional)

=== SIZE COMPARISON (vs Previous Build) ===
Previous: MyApp v2.0.9 (Build 41)

CHANGES:
- Total Size: **** MB (****%)
- DEX Size: **** MB (****%)
- Native Libs: **** MB (****%)
- Resources: +0.2 MB (****%)

LARGEST INCREASES:
1. libnative.so (arm64): +800 KB
2. classes.dex: +600 KB
3. assets/: +400 KB

NEW DEPENDENCIES:
+ com.newlibrary.sdk: 234 KB
+ androidx.camera2: 567 KB

=== RECOMMENDATIONS ===
1. Consider ProGuard/R8 optimization for DEX files
2. Compress large assets in assets/ folder
3. Use WebP format for large images
4. Review new dependencies for size impact
5. Consider dynamic feature modules for optional features
```

This comprehensive output shows exactly what you'd get from the built-in Android tools, giving you detailed breakdowns of your app's size across all dimensions you mentioned in your requirements.
